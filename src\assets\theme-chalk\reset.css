@charset "UTF-8";

body {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Aria<PERSON>, sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #000;
    -webkit-font-smoothing: antialiased
}

a {
    color: #1A6FFF;
    text-decoration: none
}

a:focus,
a:hover {
    color: #66b1ff
}

a:active {
    color: #1167fc
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: #606266;
    font-weight: inherit
}

h1:first-child,
h2:first-child,
h3:first-child,
h4:first-child,
h5:first-child,
h6:first-child,
p:first-child {
    margin-top: 0
}

h1:last-child,
h2:last-child,
h3:last-child,
h4:last-child,
h5:last-child,
h6:last-child,
p:last-child {
    margin-bottom: 0
}

h1 {
    font-size: 20px
}

h2 {
    font-size: 18px
}

h3 {
    font-size: 16px
}

h4,
h5,
h6,
p {
    font-size: inherit
}

p {
    line-height: 1.8
}

sub,
sup {
    font-size: 13px
}

small {
    font-size: 12px
}

hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee
}