// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import 'babel-polyfill'
import Vue from 'vue'
import App from './App'
import store from './store'
import router from './router'
import ElementUI from 'element-ui'
import './assets/theme-chalk/index.css'
import Echarts from 'echarts'
import Jquer<PERSON> from 'jquery'
import VueClipboards from 'vue-clipboards'
import VueDND from 'awe-dnd'

Vue.use(ElementUI);
// 参考 Element UI Vue 2 配置方法
if (ElementUI && ElementUI.Table && ElementUI.Table.props && ElementUI.Table.props.stripe) {
    // 全局设置表格斑马纹
  ElementUI.Table.props.stripe.default = true;
  // 去除表格边框
  ElementUI.Table.props.border.default = false;
  // 修改 el-dialog 默认点击遮照为不关闭
    ElementUI.Dialog.props.closeOnClickModal.default = false;
}

Vue.use(Echarts);
Vue.use(VueClipboards);
Vue.use(VueDND);
Vue.prototype.$echarts = Echarts;
Vue.prototype.$ = Jquery;
Vue.config.productionTip = false;

/* eslint-disable no-new */
new Vue({
  el: '#app',
  store,
  router,
  components: {App},
  template: '<App/>'
})
