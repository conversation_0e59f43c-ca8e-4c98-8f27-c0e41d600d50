var jQuery = require("jquery");
!function(a){var c,d,e,f,g,h,i,j,k,l,b="";a.fn.extend({cronGen:function(f){var j,k,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,ab,bb,cb,db,eb,fb,gb,hb;null==f&&(f={}),f=a.extend({},a.fn.cronGen.defaultOptions,f),j=a("<div/>",{id:"CronContainer",style:"display:none;width:300px;height:300px;"}),k=a("<div/>",{id:"CronGenMainDiv",style:"width:410px;height:320px;"}),m=a("<ul/>",{"class":"nav nav-tabs",id:"CronGenTabs"}),a("<li/>",{"class":"active"}).html(a('<a id="SecondlyTab" href="#Secondly">秒</a>')).appendTo(m),a("<li/>").html(a('<a id="MinutesTab" href="#Minutes">分钟</a>')).appendTo(m),a("<li/>").html(a('<a id="HourlyTab" href="#Hourly">小时</a>')).appendTo(m),a("<li/>").html(a('<a id="DailyTab" href="#Daily">日</a>')).appendTo(m),a("<li/>").html(a('<a id="MonthlyTab" href="#Monthly">月</a>')).appendTo(m),a("<li/>").html(a('<a id="WeeklyTab" href="#Weekly">周</a>')).appendTo(m),a("<li/>").html(a('<a id="YearlyTab" href="#Yearly">年</a>')).appendTo(m),a(m).appendTo(k),n=a("<div/>",{"class":"container-fluid",style:"margin-top: 30px;margin-left: -14px;"}),o=a("<div/>",{"class":"row-fluid"}),p=a("<div/>",{"class":"span12"}),q=a("<div/>",{"class":"tab-content",style:"border:0px; margin-top:-20px;"}),r=a("<div/>",{"class":"tab-pane active",id:"Secondly"}),s=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"1",name:"second"}).appendTo(s),a(s).append("每秒 允许的通配符[, - * /]"),a(s).appendTo(r),t=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"2",name:"second"}).appendTo(t),a(t).append("周期 从"),a("<input/>",{type:"text",id:"secondStart_0",value:"1",style:"width:35px; height:20px;"}).appendTo(t),a(t).append("-"),a("<input/>",{type:"text",id:"secondEnd_0",value:"2",style:"width:35px; height:20px;"}).appendTo(t),a(t).append("秒"),a(t).appendTo(r),u=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"3",name:"second"}).appendTo(u),a(u).append("从"),a("<input/>",{type:"text",id:"secondStart_1",value:"0",style:"width:35px; height:20px;"}).appendTo(u),a(u).append("秒开始,每"),a("<input/>",{type:"text",id:"secondEnd_1",value:"1",style:"width:35px; height:20px;"}).appendTo(u),a(u).append("秒执行一次"),a(u).appendTo(r),v=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"4",name:"second",id:"sencond_appoint"}).appendTo(v),a(v).append("指定"),a(v).appendTo(r),a(r).append('<div class="imp secondList"><input type="checkbox" style="margin-left: 5px"  value="0">00<input type="checkbox" style="margin-left: 5px"  value="1">01<input type="checkbox" style="margin-left: 5px"  value="2">02<input type="checkbox" style="margin-left: 5px"  value="3">03<input type="checkbox" style="margin-left: 5px"  value="4">04<input type="checkbox" style="margin-left: 5px"  value="5">05<input type="checkbox" style="margin-left: 5px"  value="6">06<input type="checkbox" style="margin-left: 5px"  value="7">07<input type="checkbox" style="margin-left: 5px"  value="8">08<input type="checkbox" style="margin-left: 5px"  value="9">09</div>'),a(r).append('<div class="imp secondList"><input type="checkbox" style="margin-left: 5px"  value="10">10<input type="checkbox" style="margin-left: 5px"  value="11">11<input type="checkbox" style="margin-left: 5px"  value="12">12<input type="checkbox" style="margin-left: 5px"  value="13">13<input type="checkbox" style="margin-left: 5px"  value="14">14<input type="checkbox" style="margin-left: 5px"  value="15">15<input type="checkbox" style="margin-left: 5px"  value="16">16<input type="checkbox" style="margin-left: 5px"  value="17">17<input type="checkbox" style="margin-left: 5px"  value="18">18<input type="checkbox" style="margin-left: 5px"  value="19">19</div>'),a(r).append('<div class="imp secondList"><input type="checkbox" style="margin-left: 5px"  value="20">20<input type="checkbox" style="margin-left: 5px"  value="21">21<input type="checkbox" style="margin-left: 5px"  value="22">22<input type="checkbox" style="margin-left: 5px"  value="23">23<input type="checkbox" style="margin-left: 5px"  value="24">24<input type="checkbox" style="margin-left: 5px"  value="25">25<input type="checkbox" style="margin-left: 5px"  value="26">26<input type="checkbox" style="margin-left: 5px"  value="27">27<input type="checkbox" style="margin-left: 5px"  value="28">28<input type="checkbox" style="margin-left: 5px"  value="29">29</div>'),a(r).append('<div class="imp secondList"><input type="checkbox" style="margin-left: 5px"  value="30">30<input type="checkbox" style="margin-left: 5px"  value="31">31<input type="checkbox" style="margin-left: 5px"  value="32">32<input type="checkbox" style="margin-left: 5px"  value="33">33<input type="checkbox" style="margin-left: 5px"  value="34">34<input type="checkbox" style="margin-left: 5px"  value="35">35<input type="checkbox" style="margin-left: 5px"  value="36">36<input type="checkbox" style="margin-left: 5px"  value="37">37<input type="checkbox" style="margin-left: 5px"  value="38">38<input type="checkbox" style="margin-left: 5px"  value="39">39</div>'),a(r).append('<div class="imp secondList"><input type="checkbox" style="margin-left: 5px"  value="40">40<input type="checkbox" style="margin-left: 5px"  value="41">41<input type="checkbox" style="margin-left: 5px"  value="42">42<input type="checkbox" style="margin-left: 5px"  value="43">43<input type="checkbox" style="margin-left: 5px"  value="44">44<input type="checkbox" style="margin-left: 5px"  value="45">45<input type="checkbox" style="margin-left: 5px"  value="46">46<input type="checkbox" style="margin-left: 5px"  value="47">47<input type="checkbox" style="margin-left: 5px"  value="48">48<input type="checkbox" style="margin-left: 5px"  value="49">49</div>'),a(r).append('<div class="imp secondList"><input type="checkbox" style="margin-left: 5px"  value="50">50<input type="checkbox" style="margin-left: 5px"  value="51">51<input type="checkbox" style="margin-left: 5px"  value="52">52<input type="checkbox" style="margin-left: 5px"  value="53">53<input type="checkbox" style="margin-left: 5px"  value="54">54<input type="checkbox" style="margin-left: 5px"  value="55">55<input type="checkbox" style="margin-left: 5px"  value="56">56<input type="checkbox" style="margin-left: 5px"  value="57">57<input type="checkbox" style="margin-left: 5px"  value="58">58<input type="checkbox" style="margin-left: 5px"  value="59">59</div>'),a("<input/>",{type:"hidden",id:"secondHidden"}).appendTo(r),a(r).appendTo(q),w=a("<div/>",{"class":"tab-pane",id:"Minutes"}),x=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"1",name:"min"}).appendTo(x),a(x).append("每分钟 允许的通配符[, - * /]"),a(x).appendTo(w),y=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"2",name:"min"}).appendTo(y),a(y).append("周期 从"),a("<input/>",{type:"text",id:"minStart_0",value:"1",style:"width:35px; height:20px;"}).appendTo(y),a(y).append("-"),a("<input/>",{type:"text",id:"minEnd_0",value:"2",style:"width:35px; height:20px;"}).appendTo(y),a(y).append("分钟"),a(y).appendTo(w),z=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"3",name:"min"}).appendTo(z),a(z).append("从"),a("<input/>",{type:"text",id:"minStart_1",value:"0",style:"width:35px; height:20px;"}).appendTo(z),a(z).append("分钟开始,每"),a("<input/>",{type:"text",id:"minEnd_1",value:"1",style:"width:35px; height:20px;"}).appendTo(z),a(z).append("分钟执行一次"),a(z).appendTo(w),A=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"4",name:"min",id:"min_appoint"}).appendTo(A),a(A).append("指定"),a(A).appendTo(w),a(w).append('<div class="imp minList"><input type="checkbox" style="margin-left: 5px"  value="0">00<input type="checkbox" style="margin-left: 5px"  value="1">01<input type="checkbox" style="margin-left: 5px"  value="2">02<input type="checkbox" style="margin-left: 5px"  value="3">03<input type="checkbox" style="margin-left: 5px"  value="4">04<input type="checkbox" style="margin-left: 5px"  value="5">05<input type="checkbox" style="margin-left: 5px"  value="6">06<input type="checkbox" style="margin-left: 5px"  value="7">07<input type="checkbox" style="margin-left: 5px"  value="8">08<input type="checkbox" style="margin-left: 5px"  value="9">09</div>'),a(w).append('<div class="imp minList"><input type="checkbox" style="margin-left: 5px"  value="10">10<input type="checkbox" style="margin-left: 5px"  value="11">11<input type="checkbox" style="margin-left: 5px"  value="12">12<input type="checkbox" style="margin-left: 5px"  value="13">13<input type="checkbox" style="margin-left: 5px"  value="14">14<input type="checkbox" style="margin-left: 5px"  value="15">15<input type="checkbox" style="margin-left: 5px"  value="16">16<input type="checkbox" style="margin-left: 5px"  value="17">17<input type="checkbox" style="margin-left: 5px"  value="18">18<input type="checkbox" style="margin-left: 5px"  value="19">19</div>'),a(w).append('<div class="imp minList"><input type="checkbox" style="margin-left: 5px"  value="20">20<input type="checkbox" style="margin-left: 5px"  value="21">21<input type="checkbox" style="margin-left: 5px"  value="22">22<input type="checkbox" style="margin-left: 5px"  value="23">23<input type="checkbox" style="margin-left: 5px"  value="24">24<input type="checkbox" style="margin-left: 5px"  value="25">25<input type="checkbox" style="margin-left: 5px"  value="26">26<input type="checkbox" style="margin-left: 5px"  value="27">27<input type="checkbox" style="margin-left: 5px"  value="28">28<input type="checkbox" style="margin-left: 5px"  value="29">29</div>'),a(w).append('<div class="imp minList"><input type="checkbox" style="margin-left: 5px"  value="30">30<input type="checkbox" style="margin-left: 5px"  value="31">31<input type="checkbox" style="margin-left: 5px"  value="32">32<input type="checkbox" style="margin-left: 5px"  value="33">33<input type="checkbox" style="margin-left: 5px"  value="34">34<input type="checkbox" style="margin-left: 5px"  value="35">35<input type="checkbox" style="margin-left: 5px"  value="36">36<input type="checkbox" style="margin-left: 5px"  value="37">37<input type="checkbox" style="margin-left: 5px"  value="38">38<input type="checkbox" style="margin-left: 5px"  value="39">39</div>'),a(w).append('<div class="imp minList"><input type="checkbox" style="margin-left: 5px"  value="40">40<input type="checkbox" style="margin-left: 5px"  value="41">41<input type="checkbox" style="margin-left: 5px"  value="42">42<input type="checkbox" style="margin-left: 5px"  value="43">43<input type="checkbox" style="margin-left: 5px"  value="44">44<input type="checkbox" style="margin-left: 5px"  value="45">45<input type="checkbox" style="margin-left: 5px"  value="46">46<input type="checkbox" style="margin-left: 5px"  value="47">47<input type="checkbox" style="margin-left: 5px"  value="48">48<input type="checkbox" style="margin-left: 5px"  value="49">49</div>'),a(w).append('<div class="imp minList"><input type="checkbox" style="margin-left: 5px"  value="50">50<input type="checkbox" style="margin-left: 5px"  value="51">51<input type="checkbox" style="margin-left: 5px"  value="52">52<input type="checkbox" style="margin-left: 5px"  value="53">53<input type="checkbox" style="margin-left: 5px"  value="54">54<input type="checkbox" style="margin-left: 5px"  value="55">55<input type="checkbox" style="margin-left: 5px"  value="56">56<input type="checkbox" style="margin-left: 5px"  value="57">57<input type="checkbox" style="margin-left: 5px"  value="58">58<input type="checkbox" style="margin-left: 5px"  value="59">59</div>'),a("<input/>",{type:"hidden",id:"minHidden"}).appendTo(w),a(w).appendTo(q),B=a("<div/>",{"class":"tab-pane",id:"Hourly"}),C=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"1",name:"hour"}).appendTo(C),a(C).append("每小时 允许的通配符[, - * /]"),a(C).appendTo(B),D=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"2",name:"hour"}).appendTo(D),a(D).append("周期 从"),a("<input/>",{type:"text",id:"hourStart_0",value:"1",style:"width:35px; height:20px;"}).appendTo(D),a(D).append("-"),a("<input/>",{type:"text",id:"hourEnd_0",value:"2",style:"width:35px; height:20px;"}).appendTo(D),a(D).append("小时"),a(D).appendTo(B),E=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"3",name:"hour"}).appendTo(E),a(E).append("从"),a("<input/>",{type:"text",id:"hourStart_1",value:"0",style:"width:35px; height:20px;"}).appendTo(E),a(E).append("小时开始,每"),a("<input/>",{type:"text",id:"hourEnd_1",value:"1",style:"width:35px; height:20px;"}).appendTo(E),a(E).append("小时执行一次"),a(E).appendTo(B),F=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"4",name:"hour",id:"hour_appoint"}).appendTo(F),a(F).append("指定"),a(F).appendTo(B),a(B).append('<div class="imp hourList"><input type="checkbox" style="margin-left: 5px"  value="0">00<input type="checkbox" style="margin-left: 5px"  value="1">01<input type="checkbox" style="margin-left: 5px"  value="2">02<input type="checkbox" style="margin-left: 5px"  value="3">03<input type="checkbox" style="margin-left: 5px"  value="4">04<input type="checkbox" style="margin-left: 5px"  value="5">05</div>'),a(B).append('<div class="imp hourList"><input type="checkbox" style="margin-left: 5px"  value="6">06<input type="checkbox" style="margin-left: 5px"  value="7">07<input type="checkbox" style="margin-left: 5px"  value="8">08<input type="checkbox" style="margin-left: 5px"  value="9">09<input type="checkbox" style="margin-left: 5px"  value="10">10<input type="checkbox" style="margin-left: 5px"  value="11">11</div>'),a(B).append('<div class="imp hourList"><input type="checkbox" style="margin-left: 5px"  value="12">12<input type="checkbox" style="margin-left: 5px"  value="13">13<input type="checkbox" style="margin-left: 5px"  value="14">14<input type="checkbox" style="margin-left: 5px"  value="15">15<input type="checkbox" style="margin-left: 5px"  value="16">16<input type="checkbox" style="margin-left: 5px"  value="17">17</div>'),a(B).append('<div class="imp hourList"><input type="checkbox" style="margin-left: 5px"  value="18">18<input type="checkbox" style="margin-left: 5px"  value="19">19<input type="checkbox" style="margin-left: 5px"  value="20">20<input type="checkbox" style="margin-left: 5px"  value="21">21<input type="checkbox" style="margin-left: 5px"  value="22">22<input type="checkbox" style="margin-left: 5px"  value="23">23</div>'),a("<input/>",{type:"hidden",id:"hourHidden"}).appendTo(B),a(B).appendTo(q),G=a("<div/>",{"class":"tab-pane",id:"Daily"}),H=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"1",name:"day"}).appendTo(H),a(H).append("每天 允许的通配符[, - * / L W]"),a(H).appendTo(G),I=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"2",name:"day"}).appendTo(I),a(I).append("不指定"),a(I).appendTo(G),J=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"3",name:"day"}).appendTo(J),a(J).append("周期 从"),a("<input/>",{type:"text",id:"dayStart_0",value:"1",style:"width:35px; height:20px;"}).appendTo(J),a(J).append("-"),a("<input/>",{type:"text",id:"dayEnd_0",value:"2",style:"width:35px; height:20px;"}).appendTo(J),a(J).append("日"),a(J).appendTo(G),K=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"4",name:"day"}).appendTo(K),a(K).append("从"),a("<input/>",{type:"text",id:"dayStart_1",value:"1",style:"width:35px; height:20px;"}).appendTo(K),a(K).append("日开始,每"),a("<input/>",{type:"text",id:"dayEnd_1",value:"1",style:"width:35px; height:20px;"}).appendTo(K),a(K).append("天执行一次"),a(K).appendTo(G),L=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"5",name:"day"}).appendTo(L),a(L).append("每月"),a("<input/>",{type:"text",id:"dayStart_2",value:"1",style:"width:35px; height:20px;"}).appendTo(L),a(L).append("号最近的那个工作日"),a(L).appendTo(G),M=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"6",name:"day"}).appendTo(M),a(M).append("本月最后一天"),a(M).appendTo(G),N=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"7",name:"day",id:"day_appoint"}).appendTo(N),a(N).append("指定"),a(N).appendTo(G),a(G).append('<div class="imp dayList"><input type="checkbox" style="margin-left: 5px"  value="1">1<input type="checkbox" style="margin-left: 5px"  value="2">2<input type="checkbox" style="margin-left: 5px"  value="3">3<input type="checkbox" style="margin-left: 5px"  value="4">4<input type="checkbox" style="margin-left: 5px"  value="5">5<input type="checkbox" style="margin-left: 5px"  value="6">6<input type="checkbox" style="margin-left: 5px"  value="7">7<input type="checkbox" style="margin-left: 5px"  value="8">8<input type="checkbox" style="margin-left: 5px"  value="9">9<input type="checkbox" style="margin-left: 5px"  value="10">10<input type="checkbox" style="margin-left: 5px"  value="11">11</div>'),a(G).append('<div class="imp dayList"><input type="checkbox" style="margin-left: 5px"  value="12">12<input type="checkbox" style="margin-left: 5px"  value="13">13<input type="checkbox" style="margin-left: 5px"  value="14">14<input type="checkbox" style="margin-left: 5px"  value="15">15<input type="checkbox" style="margin-left: 5px"  value="16">16<input type="checkbox" style="margin-left: 5px"  value="17">17<input type="checkbox" style="margin-left: 5px"  value="18">18<input type="checkbox" style="margin-left: 5px"  value="19">19<input type="checkbox" style="margin-left: 5px"  value="20">20<input type="checkbox" style="margin-left: 5px"  value="21">21</div>'),a(G).append('<div class="imp dayList"><input type="checkbox" style="margin-left: 5px"  value="22">22<input type="checkbox" style="margin-left: 5px"  value="23">23<input type="checkbox" style="margin-left: 5px"  value="24">24<input type="checkbox" style="margin-left: 5px"  value="25">25<input type="checkbox" style="margin-left: 5px"  value="26">26<input type="checkbox" style="margin-left: 5px"  value="27">27<input type="checkbox" style="margin-left: 5px"  value="28">28<input type="checkbox" style="margin-left: 5px"  value="29">29<input type="checkbox" style="margin-left: 5px"  value="30">30<input type="checkbox" style="margin-left: 5px"  value="31">31</div>'),a("<input/>",{type:"hidden",id:"dayHidden"}).appendTo(G),a(G).appendTo(q),O=a("<div/>",{"class":"tab-pane",id:"Weekly"}),P=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"1",name:"week"}).appendTo(P),a(P).append("每周 允许的通配符[, - * / L #]"),a(P).appendTo(O),Q=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"2",name:"week"}).appendTo(Q),a(Q).append("不指定"),a(Q).appendTo(O),R=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"3",name:"week"}).appendTo(R),a(R).append("周期 从星期"),a("<input/>",{type:"text",id:"weekStart_0",value:"1",style:"width:35px; height:20px;"}).appendTo(R),a(R).append("-"),a("<input/>",{type:"text",id:"weekEnd_0",value:"2",style:"width:35px; height:20px;"}).appendTo(R),a(R).appendTo(O),S=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"4",name:"week"}).appendTo(S),a(S).append("第"),a("<input/>",{type:"text",id:"weekStart_1",value:"1",style:"width:35px; height:20px;"}).appendTo(S),a(S).append("周的星期"),a("<input/>",{type:"text",id:"weekEnd_1",value:"1",style:"width:35px; height:20px;"}).appendTo(S),a(S).appendTo(O),T=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"5",name:"week"}).appendTo(T),a(T).append("本月最后一个星期"),a("<input/>",{type:"text",id:"weekStart_2",value:"1",style:"width:35px; height:20px;"}).appendTo(T),a(T).appendTo(O),U=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"6",name:"week",id:"week_appoint"}).appendTo(U),a(U).append("指定"),a(U).appendTo(O),a(O).append('<div class="imp weekList"><input type="checkbox" style="margin-left: 5px"  value="1">1<input type="checkbox" style="margin-left: 5px"  value="2">2<input type="checkbox" style="margin-left: 5px"  value="3">3<input type="checkbox" style="margin-left: 5px"  value="4">4<input type="checkbox" style="margin-left: 5px"  value="5">5<input type="checkbox" style="margin-left: 5px"  value="6">6<input type="checkbox" style="margin-left: 5px"  value="7">7</div>'),a("<input/>",{type:"hidden",id:"weekHidden"}).appendTo(O),a(O).appendTo(q),V=a("<div/>",{"class":"tab-pane",id:"Monthly"}),W=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"1",name:"month"}).appendTo(W),a(W).append("每月 允许的通配符[, - * /]"),a(W).appendTo(V),X=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"2",name:"month"}).appendTo(X),a(X).append("不指定"),a(X).appendTo(V),Y=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"3",name:"month"}).appendTo(Y),a(Y).append("周期 从"),a("<input/>",{type:"text",id:"monthStart_0",value:"1",style:"width:35px; height:20px;"}).appendTo(Y),a(Y).append("-"),a("<input/>",{type:"text",id:"monthEnd_0",value:"2",style:"width:35px; height:20px;"}).appendTo(Y),a(Y).append("月"),a(Y).appendTo(V),Z=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"4",name:"month"}).appendTo(Z),a(Z).append("从"),a("<input/>",{type:"text",id:"monthStart_1",value:"1",style:"width:35px; height:20px;"}).appendTo(Z),a(Z).append("日开始,每"),a("<input/>",{type:"text",id:"monthEnd_1",value:"1",style:"width:35px; height:20px;"}).appendTo(Z),a(Z).append("月执行一次"),a(Z).appendTo(V),$=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"5",name:"month",id:"mouth_appoint"}).appendTo($),a($).append("指定"),a($).appendTo(V),a(V).append('<div class="imp mouthList"><input type="checkbox" style="margin-left: 5px"  value="1">1<input type="checkbox" style="margin-left: 5px"  value="2">2<input type="checkbox" style="margin-left: 5px"  value="3">3<input type="checkbox" style="margin-left: 5px"  value="4">4<input type="checkbox" style="margin-left: 5px"  value="5">5<input type="checkbox" style="margin-left: 5px"  value="6">6</div>'),a(V).append('<div class="imp mouthList"><input type="checkbox" style="margin-left: 5px"  value="7">7<input type="checkbox" style="margin-left: 5px"  value="8">8<input type="checkbox" style="margin-left: 5px"  value="9">9<input type="checkbox" style="margin-left: 5px"  value="10">10<input type="checkbox" style="margin-left: 5px"  value="11">11<input type="checkbox" style="margin-left: 5px"  value="12">12</div>'),a("<input/>",{type:"hidden",id:"monthHidden"}).appendTo(V),a(V).appendTo(q),_=a("<div/>",{"class":"tab-pane",id:"Yearly"}),ab=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"1",name:"year"}).appendTo(ab),a(ab).append("不指定 允许的通配符[, - * /] 非必填"),a(ab).appendTo(_),bb=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"2",name:"year"}).appendTo(bb),a(bb).append("每年"),a(bb).appendTo(_),cb=a("<div/>",{"class":"line"}),a("<input/>",{type:"radio",value:"3",name:"year"}).appendTo(cb),a(cb).append("周期从"),a("<input/>",{type:"text",id:"yearStart_0",value:"2016",style:"width:45px; height:20px;"}).appendTo(cb),a(cb).append("-"),a("<input/>",{type:"text",id:"yearEnd_0",value:"2017",style:"width:45px; height:20px;"}).appendTo(cb),a(cb).append("年"),a(cb).appendTo(_),a("<input/>",{type:"hidden",id:"yearHidden"}).appendTo(_),a(_).appendTo(q),a(q).appendTo(p),b=a(this).prop("id"),a(this).prop("name",b),a(p).appendTo(o),a(o).appendTo(n),a(n).appendTo(k),a(j).append(k),db=a(this),db.hide(),eb=a("<div>").addClass("input-group"),fb=a("<input>",{type:"text",placeholder:"cron表达式...",readonly:"readonly"}).addClass("form-control").val(a(db).val()),fb.appendTo(eb),gb=a('<button class="btn btn-default"><i class="fa fa-edit"></i></button>'),hb=a("<span>").addClass("input-group-btn"),gb.appendTo(hb),hb.appendTo(eb),a(this).before(eb),c=db,d=fb,gb.popover({html:!0,content:function(){return a(j).html()},template:'<div class="popover" style="max-width:500px !important; width:425px;left:-341.656px;"><div class="arrow"></div><div class="popover-inner"><h3 class="popover-title"></h3><div class="popover-content"><p></p></div></div></div>',placement:f.direction}).on("click",function(b){b.preventDefault(),g(),i(),h(),e(),a.fn.cronGen.tools.cronParse(c.val()),a.fn.cronGen.tools.initChangeEvent(),a("#CronGenTabs a").click(function(b){b.preventDefault(),a(this).tab("show")}),a("#CronGenMainDiv select,input").change(function(){l()}),a("#CronGenMainDiv input").focus(function(){l()})})}}),e=function(){var b=[{text:"一月",val:"1"},{text:"二月",val:"2"},{text:"三月",val:"3"},{text:"四月",val:"4"},{text:"五月",val:"5"},{text:"六月",val:"6"},{text:"七月",val:"7"},{text:"八月",val:"8"},{text:"九月",val:"9"},{text:"十月",val:"10"},{text:"十一月",val:"11"},{text:"十二月",val:"12"}];a(".months").each(function(){f(this,b)})},f=function(b,c){for(var d=0;d<c.length;d++)a(b).append("<option value='"+c[d].val+"'>"+c[d].text+"</option>")},g=function(){for(var b=0;60>b;b++)24>b&&a(".hours").each(function(){a(this).append(k(b))}),a(".minutes").each(function(){a(this).append(k(b))})},h=function(){var b=[{text:"周一",val:"2"},{text:"周二",val:"3"},{text:"周三",val:"4"},{text:"周四",val:"5"},{text:"周五",val:"6"},{text:"周六",val:"7"},{text:"周天",val:"1"}];a(".week-days").each(function(){f(this,b)})},i=function(){var b=[{text:"第一个",val:"1"},{text:"第二个",val:"2"},{text:"第三个",val:"3"},{text:"第四个",val:"4"}];a(".day-order-in-month").each(function(){f(this,b)})},j=function(a){return 1==a.toString().length?"0"+a:a},k=function(a){return"<option id='"+a+"'>"+j(a)+"</option>"},l=function(){var b=a("ul#CronGenTabs li.active a").prop("id"),e="";switch(b){case"SecondlyTab":switch(a("input:radio[name=second]:checked").val()){case"1":a.fn.cronGen.tools.everyTime("second"),e=a.fn.cronGen.tools.cronResult();break;case"2":a.fn.cronGen.tools.cycle("second"),e=a.fn.cronGen.tools.cronResult();break;case"3":a.fn.cronGen.tools.startOn("second"),e=a.fn.cronGen.tools.cronResult();break;case"4":e=a.fn.cronGen.tools.cronResult()}break;case"MinutesTab":switch(a("input:radio[name=min]:checked").val()){case"1":a.fn.cronGen.tools.everyTime("min"),e=a.fn.cronGen.tools.cronResult();break;case"2":a.fn.cronGen.tools.cycle("min"),e=a.fn.cronGen.tools.cronResult();break;case"3":a.fn.cronGen.tools.startOn("min"),e=a.fn.cronGen.tools.cronResult();break;case"4":e=a.fn.cronGen.tools.cronResult()}break;case"HourlyTab":switch(a("input:radio[name=hour]:checked").val()){case"1":a.fn.cronGen.tools.everyTime("hour"),e=a.fn.cronGen.tools.cronResult();break;case"2":a.fn.cronGen.tools.cycle("hour"),e=a.fn.cronGen.tools.cronResult();break;case"3":a.fn.cronGen.tools.startOn("hour"),e=a.fn.cronGen.tools.cronResult();break;case"4":e=a.fn.cronGen.tools.cronResult()}break;case"DailyTab":switch(a("input:radio[name=day]:checked").val()){case"1":a.fn.cronGen.tools.everyTime("day"),e=a.fn.cronGen.tools.cronResult();break;case"2":a.fn.cronGen.tools.unAppoint("day"),e=a.fn.cronGen.tools.cronResult();break;case"3":a.fn.cronGen.tools.cycle("day"),e=a.fn.cronGen.tools.cronResult();break;case"4":a.fn.cronGen.tools.startOn("day"),e=a.fn.cronGen.tools.cronResult();break;case"5":a.fn.cronGen.tools.workDay("day"),e=a.fn.cronGen.tools.cronResult();break;case"6":a.fn.cronGen.tools.lastDay("day"),e=a.fn.cronGen.tools.cronResult();break;case"7":e=a.fn.cronGen.tools.cronResult()}break;case"WeeklyTab":switch(a("input:radio[name=week]:checked").val()){case"1":a.fn.cronGen.tools.everyTime("week"),e=a.fn.cronGen.tools.cronResult();break;case"2":a.fn.cronGen.tools.unAppoint("week"),e=a.fn.cronGen.tools.cronResult();break;case"3":a.fn.cronGen.tools.cycle("week"),e=a.fn.cronGen.tools.cronResult();break;case"4":a.fn.cronGen.tools.startOn("week"),e=a.fn.cronGen.tools.cronResult();break;case"5":a.fn.cronGen.tools.lastWeek("week"),e=a.fn.cronGen.tools.cronResult();break;case"6":e=a.fn.cronGen.tools.cronResult()}break;case"MonthlyTab":switch(a("input:radio[name=month]:checked").val()){case"1":a.fn.cronGen.tools.everyTime("month"),e=a.fn.cronGen.tools.cronResult();break;case"2":a.fn.cronGen.tools.unAppoint("month"),e=a.fn.cronGen.tools.cronResult();break;case"3":a.fn.cronGen.tools.cycle("month"),e=a.fn.cronGen.tools.cronResult();break;case"4":a.fn.cronGen.tools.startOn("month"),e=a.fn.cronGen.tools.cronResult();break;case"5":e=a.fn.cronGen.tools.cronResult()}break;case"YearlyTab":switch(a("input:radio[name=year]:checked").val()){case"1":a.fn.cronGen.tools.unAppoint("year"),e=a.fn.cronGen.tools.cronResult();break;case"2":a.fn.cronGen.tools.everyTime("year"),e=a.fn.cronGen.tools.cronResult();break;case"3":a.fn.cronGen.tools.cycle("year"),e=a.fn.cronGen.tools.cronResult()}}c.val(e),d.val(e)}}(jQuery),function(a){a.fn.cronGen.defaultOptions={direction:"bottom"},a.fn.cronGen.tools={everyTime:function(b){a("#"+b+"Hidden").val("*")},unAppoint:function(b){var c="?";"year"==b&&(c=""),a("#"+b+"Hidden").val(c)},cycle:function(b){var c=a("#"+b+"Start_0").val(),d=a("#"+b+"End_0").val();a("#"+b+"Hidden").val(c+"-"+d)},startOn:function(b){var c=a("#"+b+"Start_1").val(),d=a("#"+b+"End_1").val();a("#"+b+"Hidden").val(c+"/"+d)},lastDay:function(b){a("#"+b+"Hidden").val("L")},weekOfDay:function(b){var c=a("#"+b+"Start_0").val(),d=a("#"+b+"End_0").val();a("#"+b+"Hidden").val(c+"#"+d)},lastWeek:function(b){var c=a("#"+b+"Start_2").val();a("#"+b+"Hidden").val(c+"L")},workDay:function(b){var c=a("#"+b+"Start_2").val();a("#"+b+"Hidden").val(c+"W")},initChangeEvent:function(){var c,d,e,f,g,b=a(".secondList").children();a("#sencond_appoint").click(function(){this.checked&&(0==a(b).filter(":checked").length&&a(b.eq(0)).attr("checked",!0),b.eq(0).change())}),b.change(function(){var d,e,c=a("#sencond_appoint").prop("checked");c&&(d=[],b.each(function(){this.checked&&d.push(this.value)}),e="?",d.length>0&&d.length<59?e=d.join(","):59==d.length&&(e="*"),a("#secondHidden").val(e))}),c=a(".minList").children(),a("#min_appoint").click(function(){this.checked&&(0==a(c).filter(":checked").length&&a(c.eq(0)).attr("checked",!0),c.eq(0).change())}),c.change(function(){var d,e,b=a("#min_appoint").prop("checked");b&&(d=[],c.each(function(){this.checked&&d.push(this.value)}),e="?",d.length>0&&d.length<59?e=d.join(","):59==d.length&&(e="*"),a("#minHidden").val(e))}),d=a(".hourList").children(),a("#hour_appoint").click(function(){this.checked&&(0==a(d).filter(":checked").length&&a(d.eq(0)).attr("checked",!0),d.eq(0).change())}),d.change(function(){var c,e,b=a("#hour_appoint").prop("checked");b&&(c=[],d.each(function(){this.checked&&c.push(this.value)}),e="?",c.length>0&&c.length<24?e=c.join(","):24==c.length&&(e="*"),a("#hourHidden").val(e))}),e=a(".dayList").children(),a("#day_appoint").click(function(){this.checked&&(0==a(e).filter(":checked").length&&a(e.eq(0)).attr("checked",!0),e.eq(0).change())}),e.change(function(){var c,d,b=a("#day_appoint").prop("checked");b&&(c=[],e.each(function(){this.checked&&c.push(this.value)}),d="?",c.length>0&&c.length<31?d=c.join(","):31==c.length&&(d="*"),a("#dayHidden").val(d))}),f=a(".mouthList").children(),a("#mouth_appoint").click(function(){this.checked&&(0==a(f).filter(":checked").length&&a(f.eq(0)).attr("checked",!0),f.eq(0).change())}),f.change(function(){var c,d,b=a("#mouth_appoint").prop("checked");b&&(c=[],f.each(function(){this.checked&&c.push(this.value)}),d="?",c.length>0&&c.length<12?d=c.join(","):12==c.length&&(d="*"),a("#monthHidden").val(d))}),g=a(".weekList").children(),a("#week_appoint").click(function(){this.checked&&(0==a(g).filter(":checked").length&&a(g.eq(0)).attr("checked",!0),g.eq(0).change())}),g.change(function(){var c,d,b=a("#week_appoint").prop("checked");b&&(c=[],g.each(function(){this.checked&&c.push(this.value)}),d="?",c.length>0&&c.length<7?d=c.join(","):7==c.length&&(d="*"),a("#weekHidden").val(d))})},initObj:function(b,c){var f,d=null,e=a("input[name='"+c+"'");if("*"==b)e.eq(0).attr("checked","checked");else if(b.split("-").length>1)d=b.split("-"),e.eq(1).attr("checked","checked"),a("#"+c+"Start_0").val(d[0]),a("#"+c+"End_0").val(d[1]);else if(b.split("/").length>1)d=b.split("/"),e.eq(2).attr("checked","checked"),a("#"+c+"Start_1").val(d[0]),a("#"+c+"End_1").val(d[1]);else if(e.eq(3).attr("checked","checked"),"?"!=b)for(d=b.split(","),f=0;f<d.length;f++)a("."+c+"List input[value='"+d[f]+"']").attr("checked","checked")},initDay:function(b){var e,c=null,d=a("input[name='day'");if("*"==b)d.eq(0).attr("checked","checked");else if("?"==b)d.eq(1).attr("checked","checked");else if(b.split("-").length>1)c=b.split("-"),d.eq(2).attr("checked","checked"),a("#dayStart_0").val(c[0]),a("#dayEnd_0").val(c[1]);
else if(b.split("/").length>1)c=b.split("/"),d.eq(3).attr("checked","checked"),a("#dayStart_1").val(c[0]),a("#dayEnd_1").val(c[1]);else if(b.split("W").length>1)c=b.split("W"),d.eq(4).attr("checked","checked"),a("#dayStart_2").val(c[0]);else if("L"==b)d.eq(5).attr("checked","checked");else for(d.eq(6).attr("checked","checked"),c=b.split(","),e=0;e<c.length;e++)a(".dayList input[value='"+c[e]+"']").attr("checked","checked")},initMonth:function(b){var e,c=null,d=a("input[name='mouth'");if("*"==b)d.eq(0).attr("checked","checked");else if("?"==b)d.eq(1).attr("checked","checked");else if(b.split("-").length>1)c=b.split("-"),d.eq(2).attr("checked","checked"),a("#mouthStart_0").val(c[0]),a("#mouthEnd_0").val(c[1]);else if(b.split("/").length>1)c=b.split("/"),d.eq(3).attr("checked","checked"),a("#mouthStart_1").val(c[0]),a("#mouthEnd_1").val(c[1]);else for(d.eq(4).attr("checked","checked"),c=b.split(","),e=0;e<c.length;e++)a(".mouthList input[value='"+c[e]+"']").attr("checked","checked")},initWeek:function(b){var e,c=null,d=a("input[name='week'");if("*"==b)d.eq(0).attr("checked","checked");else if("?"==b)d.eq(1).attr("checked","checked");else if(b.split("/").length>1)c=b.split("/"),d.eq(2).attr("checked","checked"),a("#weekStart_0").val(c[0]),a("#weekEnd_0").val(c[1]);else if(b.split("-").length>1)c=b.split("-"),d.eq(3).attr("checked","checked"),a("#weekStart_1").val(c[0]),a("#weekEnd_1").val(c[1]);else if(b.split("L").length>1)c=b.split("L"),d.eq(4).attr("checked","checked"),a("#weekStart_2").val(c[0]);else for(d.eq(5).attr("checked","checked"),c=b.split(","),e=0;e<c.length;e++)a(".weekList input[value='"+c[e]+"']").attr("checked","checked")},initYear:function(b){var c=null,d=a("input[name='year'");"*"==b?d.eq(1).attr("checked","checked"):b.split("-").length>1&&(c=b.split("-"),d.eq(2).attr("checked","checked"),a("#yearStart_0").val(c[0]),a("#yearEnd_0").val(c[1]))},cronParse:function(b){if(b){var c=b.split(" ");a("input[name=secondHidden]").val(c[0]),a("input[name=minHidden]").val(c[1]),a("input[name=hourHidden]").val(c[2]),a("input[name=dayHidden]").val(c[3]),a("input[name=mouthHidden]").val(c[4]),a("input[name=weekHidden]").val(c[5]),a.fn.cronGen.tools.initObj(c[0],"second"),a.fn.cronGen.tools.initObj(c[1],"min"),a.fn.cronGen.tools.initObj(c[2],"hour"),a.fn.cronGen.tools.initDay(c[3]),a.fn.cronGen.tools.initMonth(c[4]),a.fn.cronGen.tools.initWeek(c[5]),c.length>6&&(a("input[name=yearHidden]").val(c[6]),a.fn.cronGen.tools.initYear(c[6]))}},cronResult:function(){var b,d,e,f,g,h,i,c=a("#secondHidden").val();return c=""==c?"*":c,d=a("#minHidden").val(),d=""==d?"*":d,e=a("#hourHidden").val(),e=""==e?"*":e,f=a("#dayHidden").val(),f=""==f?"*":f,g=a("#monthHidden").val(),g=""==g?"*":g,h=a("#weekHidden").val(),h=""==h?"?":h,i=a("#yearHidden").val(),b=""!=i?c+" "+d+" "+e+" "+f+" "+g+" "+h+" "+i:c+" "+d+" "+e+" "+f+" "+g+" "+h}}}(jQuery);